/* CSS Custom Properties */
:root {
    --primary-bg: #080f17;
    --secondary-bg: rgba(255, 255, 255, 0.04);
    --accent-color: #c1f17e;
    --text-primary: #d6dde6;
    --text-secondary: rgba(214, 221, 230, 0.7);
    --text-muted: rgba(214, 221, 230, 0.4);
    --border-color: rgba(255, 255, 255, 0.2);
    --border-subtle: rgba(255, 255, 255, 0.1);
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    background-color: var(--primary-bg);
    border-bottom: 1px solid var(--border-subtle);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    flex-direction: column;
    gap: 4px;
    padding: var(--spacing-sm);
}

.hamburger {
    width: 20px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.3s ease;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo-icon {
    font-size: 32px;
    color: var(--accent-color);
}

.logo-text {
    font-size: 24px;
    font-weight: 800;
    color: var(--accent-color);
    letter-spacing: -0.02em;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-icon {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 20px;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.nav-icon:hover {
    background-color: var(--secondary-bg);
}

.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--border-color);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main Layout */
.main-layout {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    display: grid;
    grid-template-columns: 300px 1fr 400px;
    gap: var(--spacing-xl);
    min-height: calc(100vh - 100px);
}

/* Sidebar */
.sidebar {
    background-color: var(--secondary-bg);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.sidebar-content {
    padding: var(--spacing-lg);
}

.sidebar-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.filter-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-subtle);
}

.filter-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

/* Search Input */
.search-input {
    position: relative;
    margin-bottom: var(--spacing-md);
}

.search-input input {
    width: 100%;
    padding: var(--spacing-md);
    padding-right: 40px;
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 14px;
}

.search-input input::placeholder {
    color: var(--text-muted);
}

.search-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

/* Radio and Checkbox Options */
.radio-option,
.checkbox-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.radio-option:hover,
.checkbox-option:hover {
    background-color: var(--secondary-bg);
}

.radio-option input,
.checkbox-option input {
    display: none;
}

.radio-custom,
.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-custom {
    border-radius: 4px;
}

.radio-option input:checked + .radio-custom {
    border-color: var(--accent-color);
}

.radio-option input:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: var(--accent-color);
    border-radius: 50%;
}

.checkbox-option input:checked + .checkbox-custom {
    border-color: var(--accent-color);
    background-color: var(--accent-color);
}

.checkbox-option input:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-bg);
    font-size: 12px;
    font-weight: bold;
}

.radio-label,
.checkbox-label {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

/* Custom Range */
.custom-range {
    margin-top: var(--spacing-md);
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    margin-left: 28px;
}

.range-input {
    flex: 1;
    padding: var(--spacing-sm);
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 13px;
}

.range-input::placeholder {
    color: var(--text-muted);
}

.range-separator {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Content Area */
.content-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Search Section */
.search-section {
    background-color: var(--secondary-bg);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

.main-search {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.main-search input {
    flex: 1;
    padding: var(--spacing-md);
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 16px;
}

.main-search input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--accent-color);
    color: var(--primary-bg);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.search-btn:hover {
    opacity: 0.9;
}

.quick-filters {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.quick-filter {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-filter:hover,
.quick-filter.active {
    background-color: var(--accent-color);
    color: var(--primary-bg);
    border-color: var(--accent-color);
}

/* Results Header */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.results-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
}

.results-count {
    font-size: 14px;
    color: var(--text-secondary);
}

.view-toggle {
    display: flex;
    gap: var(--spacing-xs);
}

.view-btn {
    padding: var(--spacing-sm);
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn:hover,
.view-btn.active {
    background-color: var(--accent-color);
    color: var(--primary-bg);
    border-color: var(--accent-color);
}

/* Kost Grid */
.kost-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

/* Kost Card */
.kost-card {
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.kost-card:hover {
    transform: translateY(-4px);
    border-color: var(--accent-color);
    box-shadow: 0 8px 32px rgba(193, 241, 126, 0.1);
}

.kost-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.kost-badge {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    background-color: var(--accent-color);
    color: var(--primary-bg);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-weight: 600;
}

.kost-favorite {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 32px;
    height: 32px;
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.kost-favorite:hover {
    background-color: var(--accent-color);
    color: var(--primary-bg);
}

.kost-content {
    padding: var(--spacing-md);
}

.kost-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.4;
}

.kost-location {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.kost-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: var(--spacing-sm);
}

.kost-price-period {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
}

.kost-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.kost-feature {
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-subtle);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

.kost-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-subtle);
}

.kost-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.kost-stars {
    color: #fbbf24;
    font-size: 14px;
}

.kost-rating-text {
    font-size: 14px;
    color: var(--text-secondary);
}

.kost-distance {
    font-size: 12px;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Map Markers */
.map-marker {
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: var(--accent-color);
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--primary-bg);
    font-weight: bold;
}

.map-marker:hover {
    transform: scale(1.2);
    z-index: 10;
}

.map-marker.active {
    background-color: #ef4444;
    transform: scale(1.3);
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* List View */
.kost-grid.list-view {
    grid-template-columns: 1fr;
}

.kost-grid.list-view .kost-card {
    display: flex;
    flex-direction: row;
    align-items: stretch;
}

.kost-grid.list-view .kost-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.kost-grid.list-view .kost-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* No Results */
.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.no-results h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Animations */
.kost-card {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus States */
button:focus,
input:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* Load More */
.load-more {
    text-align: center;
    margin-top: var(--spacing-lg);
}

.load-more-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.load-more-btn:hover {
    background-color: var(--accent-color);
    color: var(--primary-bg);
    border-color: var(--accent-color);
}

/* Map Area */
.map-area {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.map-container {
    position: relative;
    background-color: var(--secondary-bg);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.map-placeholder {
    height: 500px;
    background: linear-gradient(135deg, var(--secondary-bg) 0%, rgba(193, 241, 126, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.map-overlay {
    text-align: center;
    color: var(--text-secondary);
}

.map-overlay h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.map-controls {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.map-control {
    width: 40px;
    height: 40px;
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.map-control:hover {
    background-color: var(--accent-color);
    color: var(--primary-bg);
    border-color: var(--accent-color);
}

/* Mobile Overlay */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 90;
}

.mobile-overlay.active {
    display: block;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-layout {
        grid-template-columns: 280px 1fr 350px;
        gap: var(--spacing-lg);
    }
}

@media (max-width: 1024px) {
    .main-layout {
        grid-template-columns: 1fr 350px;
        gap: var(--spacing-lg);
    }
    
    .sidebar {
        position: fixed;
        top: 0;
        left: -320px;
        width: 300px;
        height: 100vh;
        z-index: 95;
        transition: left 0.3s ease;
        border-radius: 0;
        border-left: none;
    }
    
    .sidebar.active {
        left: 0;
    }
    
    .menu-toggle {
        display: flex;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }
    
    .main-layout {
        grid-template-columns: 1fr;
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
    
    .map-area {
        position: static;
        order: -1;
    }
    
    .map-placeholder {
        height: 300px;
    }
    
    .results-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .kost-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-filters {
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }
    
    .quick-filter {
        white-space: nowrap;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--spacing-sm);
    }
    
    .main-layout {
        padding: var(--spacing-sm);
    }
    
    .logo-text {
        font-size: 20px;
    }
    
    .nav-right {
        gap: var(--spacing-sm);
    }
    
    .main-search {
        flex-direction: column;
    }
    
    .search-btn {
        align-self: stretch;
    }
}
