<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KostFinder - Platform Pencarian Kost Modern</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <header class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="menu-toggle" id="menuToggle" aria-label="Toggle menu">
                    <span class="hamburger"></span>
                    <span class="hamburger"></span>
                    <span class="hamburger"></span>
                </button>
                <div class="logo">
                    <div class="logo-icon">🏠</div>
                    <h1 class="logo-text">KostFinder</h1>
                </div>
            </div>
            <div class="nav-right">
                <button class="nav-icon" aria-label="Search">🔍</button>
                <button class="nav-icon" aria-label="Apps">⊞</button>
                <div class="profile-avatar">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Profile" />
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-layout">
        <!-- Sidebar Filters -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <h2 class="sidebar-title">Filter Pencarian</h2>
                
                <!-- Location Filter -->
                <div class="filter-section">
                    <h3 class="filter-title">Lokasi</h3>
                    <div class="search-input">
                        <input type="text" placeholder="Cari lokasi..." id="locationSearch">
                        <span class="search-icon">🔍</span>
                    </div>
                </div>

                <!-- Price Range Filter -->
                <div class="filter-section">
                    <h3 class="filter-title">Rentang Harga</h3>
                    <div class="price-options">
                        <label class="radio-option">
                            <input type="radio" name="price" value="500000-1000000">
                            <span class="radio-custom"></span>
                            <span class="radio-label">Rp 500.000 - Rp 1.000.000</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="price" value="1000000-1500000">
                            <span class="radio-custom"></span>
                            <span class="radio-label">Rp 1.000.000 - Rp 1.500.000</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="price" value="1500000-2000000">
                            <span class="radio-custom"></span>
                            <span class="radio-label">Rp 1.500.000 - Rp 2.000.000</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="price" value="2000000-2500000">
                            <span class="radio-custom"></span>
                            <span class="radio-label">Rp 2.000.000 - Rp 2.500.000</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="price" value="2500000+">
                            <span class="radio-custom"></span>
                            <span class="radio-label">Rp 2.500.000+</span>
                        </label>
                    </div>
                    <div class="custom-range">
                        <label class="radio-option">
                            <input type="radio" name="price" value="custom">
                            <span class="radio-custom"></span>
                            <span class="radio-label">Custom Range</span>
                        </label>
                        <div class="range-inputs">
                            <input type="number" placeholder="Min" class="range-input">
                            <span class="range-separator">to</span>
                            <input type="number" placeholder="Max" class="range-input">
                        </div>
                    </div>
                </div>

                <!-- Facilities Filter -->
                <div class="filter-section">
                    <h3 class="filter-title">Fasilitas</h3>
                    <div class="facility-options">
                        <label class="checkbox-option">
                            <input type="checkbox" value="wifi">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">WiFi</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" value="ac">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">AC</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" value="parking">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Parkir</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" value="laundry">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Laundry</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" value="kitchen">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Dapur</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" value="security">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Security 24/7</span>
                        </label>
                    </div>
                </div>

                <!-- Room Type Filter -->
                <div class="filter-section">
                    <h3 class="filter-title">Jenis Kamar</h3>
                    <div class="room-type-options">
                        <label class="checkbox-option">
                            <input type="checkbox" value="single">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Kamar Single</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" value="shared">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Kamar Sharing</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" value="studio">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Studio</span>
                        </label>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Content Area -->
        <section class="content-area">
            <!-- Search Bar -->
            <div class="search-section">
                <div class="main-search">
                    <input type="text" placeholder="Cari kost berdasarkan nama, lokasi, atau fasilitas..." id="mainSearch">
                    <button class="search-btn">Cari</button>
                </div>
                <div class="quick-filters">
                    <button class="quick-filter active">Semua</button>
                    <button class="quick-filter">Terdekat</button>
                    <button class="quick-filter">Termurah</button>
                    <button class="quick-filter">Rating Tinggi</button>
                    <button class="quick-filter">Terbaru</button>
                </div>
            </div>

            <!-- Results Header -->
            <div class="results-header">
                <h2 class="results-title">Hasil Pencarian Kost</h2>
                <div class="results-count">Menampilkan 24 dari 156 kost</div>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid">⊞</button>
                    <button class="view-btn" data-view="list">☰</button>
                </div>
            </div>

            <!-- Kost Cards Grid -->
            <div class="kost-grid" id="kostGrid">
                <!-- Kost cards will be dynamically generated -->
            </div>

            <!-- Load More -->
            <div class="load-more">
                <button class="load-more-btn">Muat Lebih Banyak</button>
            </div>
        </section>

        <!-- Map Area -->
        <section class="map-area">
            <div class="map-container">
                <div class="map-placeholder" id="mapContainer">
                    <div class="map-overlay">
                        <h3>Peta Interaktif</h3>
                        <p>Klik untuk melihat lokasi kost</p>
                    </div>
                    <!-- Map markers will be added here -->
                </div>
                <div class="map-controls">
                    <button class="map-control" id="zoomIn">+</button>
                    <button class="map-control" id="zoomOut">-</button>
                    <button class="map-control" id="myLocation">📍</button>
                </div>
            </div>
        </section>
    </main>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <script src="script.js"></script>
</body>
</html>
