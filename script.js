// KostFinder JavaScript - Modern Kost Search Platform

// Sample data for kost listings
const kostData = [
    {
        id: 1,
        title: "Kost Modern Dekat Kampus",
        location: "Jl. Sudirman No. 123, Jakarta Pusat",
        price: 1200000,
        rating: 4.8,
        distance: "0.5 km",
        image: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=400&h=300&fit=crop",
        features: ["WiFi", "AC", "Parkir", "Security"],
        type: "single",
        badge: "Populer"
    },
    {
        id: 2,
        title: "Kost Nyaman Budget Friendly",
        location: "Jl. Kebon Jeruk No. 45, Jakarta Barat",
        price: 800000,
        rating: 4.5,
        distance: "1.2 km",
        image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop",
        features: ["WiFi", "Laundry", "<PERSON>pur"],
        type: "shared",
        badge: "<PERSON><PERSON>"
    },
    {
        id: 3,
        title: "Studio Premium Fully Furnished",
        location: "Jl. Thamrin No. 67, Jakarta Pusat",
        price: 2500000,
        rating: 4.9,
        distance: "0.8 km",
        image: "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=400&h=300&fit=crop",
        features: ["WiFi", "AC", "Parkir", "Security", "Dapur", "Laundry"],
        type: "studio",
        badge: "Premium"
    },
    {
        id: 4,
        title: "Kost Strategis Dekat MRT",
        location: "Jl. Blok M No. 89, Jakarta Selatan",
        price: 1500000,
        rating: 4.6,
        distance: "0.3 km",
        image: "https://images.unsplash.com/photo-1484154218962-a197022b5858?w=400&h=300&fit=crop",
        features: ["WiFi", "AC", "Security", "Parkir"],
        type: "single",
        badge: "Strategis"
    },
    {
        id: 5,
        title: "Kost Cozy dengan Fasilitas Lengkap",
        location: "Jl. Kemang No. 12, Jakarta Selatan",
        price: 1800000,
        rating: 4.7,
        distance: "1.5 km",
        image: "https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=400&h=300&fit=crop",
        features: ["WiFi", "AC", "Laundry", "Dapur", "Security"],
        type: "single",
        badge: "Lengkap"
    },
    {
        id: 6,
        title: "Kost Sharing Ekonomis",
        location: "Jl. Cikini No. 34, Jakarta Pusat",
        price: 600000,
        rating: 4.3,
        distance: "2.1 km",
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
        features: ["WiFi", "Laundry", "Dapur"],
        type: "shared",
        badge: "Ekonomis"
    }
];

// Global state
let filteredData = [...kostData];
let currentView = 'grid';
let activeFilters = {
    price: null,
    facilities: [],
    roomType: [],
    location: ''
};

// DOM Elements
const menuToggle = document.getElementById('menuToggle');
const sidebar = document.getElementById('sidebar');
const mobileOverlay = document.getElementById('mobileOverlay');
const kostGrid = document.getElementById('kostGrid');
const mainSearch = document.getElementById('mainSearch');
const locationSearch = document.getElementById('locationSearch');
const quickFilters = document.querySelectorAll('.quick-filter');
const viewButtons = document.querySelectorAll('.view-btn');
const mapContainer = document.getElementById('mapContainer');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    renderKostCards();
    initializeMap();
    updateResultsCount();
});

// Event Listeners
function initializeEventListeners() {
    // Mobile menu toggle
    menuToggle.addEventListener('click', toggleMobileMenu);
    mobileOverlay.addEventListener('click', closeMobileMenu);

    // Search functionality
    mainSearch.addEventListener('input', handleMainSearch);
    locationSearch.addEventListener('input', handleLocationSearch);

    // Quick filters
    quickFilters.forEach(filter => {
        filter.addEventListener('click', handleQuickFilter);
    });

    // View toggle
    viewButtons.forEach(btn => {
        btn.addEventListener('click', handleViewToggle);
    });

    // Price filters
    const priceRadios = document.querySelectorAll('input[name="price"]');
    priceRadios.forEach(radio => {
        radio.addEventListener('change', handlePriceFilter);
    });

    // Facility filters
    const facilityCheckboxes = document.querySelectorAll('.facility-options input[type="checkbox"]');
    facilityCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleFacilityFilter);
    });

    // Room type filters
    const roomTypeCheckboxes = document.querySelectorAll('.room-type-options input[type="checkbox"]');
    roomTypeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleRoomTypeFilter);
    });

    // Map controls
    document.getElementById('zoomIn').addEventListener('click', () => handleMapControl('zoomIn'));
    document.getElementById('zoomOut').addEventListener('click', () => handleMapControl('zoomOut'));
    document.getElementById('myLocation').addEventListener('click', () => handleMapControl('myLocation'));

    // Load more button
    document.querySelector('.load-more-btn').addEventListener('click', handleLoadMore);
}

// Mobile menu functions
function toggleMobileMenu() {
    sidebar.classList.toggle('active');
    mobileOverlay.classList.toggle('active');
    document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
}

function closeMobileMenu() {
    sidebar.classList.remove('active');
    mobileOverlay.classList.remove('active');
    document.body.style.overflow = '';
}

// Search functions
function handleMainSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    filteredData = kostData.filter(kost => 
        kost.title.toLowerCase().includes(searchTerm) ||
        kost.location.toLowerCase().includes(searchTerm) ||
        kost.features.some(feature => feature.toLowerCase().includes(searchTerm))
    );
    applyFilters();
}

function handleLocationSearch(e) {
    activeFilters.location = e.target.value.toLowerCase();
    applyFilters();
}

// Filter functions
function handleQuickFilter(e) {
    // Remove active class from all quick filters
    quickFilters.forEach(filter => filter.classList.remove('active'));
    // Add active class to clicked filter
    e.target.classList.add('active');
    
    const filterType = e.target.textContent;
    
    switch(filterType) {
        case 'Semua':
            filteredData = [...kostData];
            break;
        case 'Terdekat':
            filteredData = [...kostData].sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
            break;
        case 'Termurah':
            filteredData = [...kostData].sort((a, b) => a.price - b.price);
            break;
        case 'Rating Tinggi':
            filteredData = [...kostData].sort((a, b) => b.rating - a.rating);
            break;
        case 'Terbaru':
            filteredData = [...kostData].reverse();
            break;
    }
    
    applyFilters();
}

function handlePriceFilter(e) {
    activeFilters.price = e.target.value;
    applyFilters();
}

function handleFacilityFilter(e) {
    const facility = e.target.value;
    if (e.target.checked) {
        activeFilters.facilities.push(facility);
    } else {
        activeFilters.facilities = activeFilters.facilities.filter(f => f !== facility);
    }
    applyFilters();
}

function handleRoomTypeFilter(e) {
    const roomType = e.target.value;
    if (e.target.checked) {
        activeFilters.roomType.push(roomType);
    } else {
        activeFilters.roomType = activeFilters.roomType.filter(t => t !== roomType);
    }
    applyFilters();
}

function applyFilters() {
    let filtered = [...filteredData];

    // Apply price filter
    if (activeFilters.price && activeFilters.price !== 'custom') {
        const [min, max] = activeFilters.price.split('-').map(p => parseInt(p.replace('+', '')));
        filtered = filtered.filter(kost => {
            if (max) {
                return kost.price >= min && kost.price <= max;
            } else {
                return kost.price >= min;
            }
        });
    }

    // Apply facility filter
    if (activeFilters.facilities.length > 0) {
        filtered = filtered.filter(kost => 
            activeFilters.facilities.every(facility => 
                kost.features.some(feature => feature.toLowerCase().includes(facility.toLowerCase()))
            )
        );
    }

    // Apply room type filter
    if (activeFilters.roomType.length > 0) {
        filtered = filtered.filter(kost => 
            activeFilters.roomType.includes(kost.type)
        );
    }

    // Apply location filter
    if (activeFilters.location) {
        filtered = filtered.filter(kost => 
            kost.location.toLowerCase().includes(activeFilters.location)
        );
    }

    filteredData = filtered;
    renderKostCards();
    updateResultsCount();
    updateMapMarkers();
}

// View toggle
function handleViewToggle(e) {
    viewButtons.forEach(btn => btn.classList.remove('active'));
    e.target.classList.add('active');
    currentView = e.target.dataset.view;
    
    if (currentView === 'list') {
        kostGrid.classList.add('list-view');
    } else {
        kostGrid.classList.remove('list-view');
    }
}

// Render functions
function renderKostCards() {
    kostGrid.innerHTML = '';
    
    if (filteredData.length === 0) {
        kostGrid.innerHTML = `
            <div class="no-results">
                <h3>Tidak ada kost yang ditemukan</h3>
                <p>Coba ubah filter pencarian Anda</p>
            </div>
        `;
        return;
    }

    filteredData.forEach(kost => {
        const card = createKostCard(kost);
        kostGrid.appendChild(card);
    });
}

function createKostCard(kost) {
    const card = document.createElement('div');
    card.className = 'kost-card';
    card.innerHTML = `
        <div class="kost-image" style="background-image: url('${kost.image}')">
            <div class="kost-badge">${kost.badge}</div>
            <button class="kost-favorite" onclick="toggleFavorite(${kost.id})">♡</button>
        </div>
        <div class="kost-content">
            <h3 class="kost-title">${kost.title}</h3>
            <div class="kost-location">📍 ${kost.location}</div>
            <div class="kost-price">
                Rp ${kost.price.toLocaleString('id-ID')}
                <span class="kost-price-period">/bulan</span>
            </div>
            <div class="kost-features">
                ${kost.features.map(feature => `<span class="kost-feature">${feature}</span>`).join('')}
            </div>
            <div class="kost-footer">
                <div class="kost-rating">
                    <span class="kost-stars">★★★★★</span>
                    <span class="kost-rating-text">${kost.rating}</span>
                </div>
                <div class="kost-distance">🚶 ${kost.distance}</div>
            </div>
        </div>
    `;
    
    card.addEventListener('click', () => showKostDetail(kost));
    return card;
}

// Map functions
function initializeMap() {
    // Create sample markers for demonstration
    kostData.forEach((kost, index) => {
        const marker = document.createElement('div');
        marker.className = 'map-marker';
        marker.textContent = index + 1;
        marker.style.left = `${20 + (index * 60)}px`;
        marker.style.top = `${50 + (index % 3) * 80}px`;
        marker.addEventListener('click', () => highlightKost(kost.id));
        mapContainer.appendChild(marker);
    });
}

function updateMapMarkers() {
    const markers = mapContainer.querySelectorAll('.map-marker');
    markers.forEach((marker, index) => {
        const kost = kostData[index];
        if (filteredData.some(k => k.id === kost.id)) {
            marker.style.display = 'flex';
        } else {
            marker.style.display = 'none';
        }
    });
}

function handleMapControl(action) {
    switch(action) {
        case 'zoomIn':
            console.log('Zoom in');
            break;
        case 'zoomOut':
            console.log('Zoom out');
            break;
        case 'myLocation':
            console.log('Show my location');
            break;
    }
}

// Utility functions
function updateResultsCount() {
    const resultsCount = document.querySelector('.results-count');
    resultsCount.textContent = `Menampilkan ${filteredData.length} dari ${kostData.length} kost`;
}

function toggleFavorite(kostId) {
    console.log(`Toggle favorite for kost ${kostId}`);
    // Implement favorite functionality
}

function highlightKost(kostId) {
    console.log(`Highlight kost ${kostId}`);
    // Implement kost highlighting
}

function showKostDetail(kost) {
    console.log(`Show detail for kost ${kost.id}`);
    // Implement kost detail modal/page
}

function handleLoadMore() {
    console.log('Load more kost');
    // Implement pagination
}

// Responsive behavior
window.addEventListener('resize', function() {
    if (window.innerWidth > 1024) {
        closeMobileMenu();
    }
});

// Export functions for global access
window.toggleFavorite = toggleFavorite;
window.highlightKost = highlightKost;
window.showKostDetail = showKostDetail;
